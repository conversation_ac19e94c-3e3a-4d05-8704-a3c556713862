using Godot;

public partial class PlayerLight : PointLight2D
{
	[Export] public float LightRadius { get; set; } = 64.0f;
	[Export] public Color LightColor { get; set; } = Colors.White;
	[Export] public float LightEnergy { get; set; } = 1.0f;
	[Export] public bool OnlyShowAtNight { get; set; } = true;

	private DayNightManager _dayNightManager;

	public override void _Ready()
	{
		// This class now inherits from PointLight2D, so we can configure ourselves directly
		Enabled = true;
		Energy = LightEnergy;
		Color = LightColor;
		Scale = Vector2.One * (LightRadius / 64.0f); // Scale the light

		// Find the DayNightManager
		_dayNightManager = GetNode<DayNightManager>("/root/world/DayNight");
		if (_dayNightManager == null)
		{
			// Try alternative path
			_dayNightManager = GetTree().GetFirstNodeInGroup("DayNightManager") as DayNightManager;
			if (_dayNightManager == null)
			{
				GD.PrintErr("PlayerLight: DayNightManager not found!");
			}
		}

		GD.Print($"PlayerLight: Initialized with radius {LightRadius}");
	}

	public override void _Process(double delta)
	{
		if (_dayNightManager == null) return;

		// Show/hide light based on day/night cycle
		if (OnlyShowAtNight)
		{
			Visible = _dayNightManager.IsNight();
		}
		else
		{
			Visible = true;
		}
	}

	public void SetLightRadius(float radius)
	{
		LightRadius = radius;
		Scale = Vector2.One * (radius / 64.0f);
		GD.Print($"PlayerLight: Radius set to {radius}");
	}

	public void SetLightColor(Color color)
	{
		LightColor = color;
		Color = color;
	}

	public void SetLightEnergy(float energy)
	{
		LightEnergy = energy;
		Energy = energy;
	}

	public void SetOnlyShowAtNight(bool nightOnly)
	{
		OnlyShowAtNight = nightOnly;
		GD.Print($"PlayerLight: OnlyShowAtNight set to {nightOnly}");
	}
}
